/*
 * 2D卷积GPU优化性能测试主程序
 *
 * 功能：
 * 1. 解析命令行参数配置卷积操作
 * 2. 分配GPU和CPU内存
 * 3. 初始化测试数据
 * 4. 执行GPU卷积计算并测量性能
 * 5. 使用CPU参考实现验证正确性
 *
 * 使用方法：
 * ./conv2dfp16demo n c h w k r s u v p q
 *
 * 参数说明：
 * n: batch size (批处理大小)
 * c: input channels (输入通道数)
 * h: input height (输入高度)
 * w: input width (输入宽度)
 * k: output channels (输出通道数)
 * r: kernel height (卷积核高度)
 * s: kernel width (卷积核宽度)
 * u: stride height (高度步长)
 * v: stride width (宽度步长)
 * p: padding height (高度填充)
 * q: padding width (宽度填充)
 */

#include <stdio.h>              // 标准输入输出
#include <math.h>               // 数学函数库
#include "hip/hip_runtime.h"    // HIP运行时API
#include <hip/hip_ext.h>        // HIP扩展API
#include "verfiy.h"             // CPU参考实现
#include "conv2d.h"             // 卷积相关数据结构

/**
 * 主函数：2D卷积性能测试和验证
 *
 * @param argc 命令行参数个数
 * @param argv 命令行参数数组
 * @return 0表示成功，非0表示失败
 */
int main(int argc, char**argv)
{
    // ==================== 解析命令行参数 ====================
    int n = atoi(argv[1]);   // 批处理大小
    int c = atoi(argv[2]);   // 输入通道数
    int h = atoi(argv[3]);   // 输入特征图高度
    int w = atoi(argv[4]);   // 输入特征图宽度
    int k = atoi(argv[5]);   // 输出通道数/卷积核数量
    int r = atoi(argv[6]);   // 卷积核高度
    int s = atoi(argv[7]);   // 卷积核宽度
    int u = atoi(argv[8]);   // 高度方向步长
    int v = atoi(argv[9]);   // 宽度方向步长
    int p = atoi(argv[10]);  // 高度方向填充
    int q = atoi(argv[11]);  // 宽度方向填充

    // ==================== 计算输出尺寸 ====================
    // 使用标准卷积输出尺寸公式
    int outh = (h - r + 2*p)/u + 1;  // 输出特征图高度
    int outw = (w - s + 2*q)/v + 1;  // 输出特征图宽度

    // ==================== 内存分配 ====================
    // 分配主机端内存
    _Float16 *pIn       = (_Float16*)malloc(n*c*h*w*sizeof(_Float16));        // 输入特征图
    _Float16 *pWeight   = (_Float16*)malloc(k*c*r*s*sizeof(_Float16));        // 卷积核权重
    _Float16 *pOut      = (_Float16*)malloc(n*k*outh*outw*sizeof(_Float16));  // CPU计算结果
    _Float16 *pOut_host = (_Float16*)malloc(n*k*outh*outw*sizeof(_Float16));  // GPU计算结果

    // 分配GPU设备端内存
    _Float16 *pIn_device, *pWeight_device, *pOut_device;
    hipMalloc((void**)&pIn_device, n*c*h*w*sizeof(_Float16));        // GPU输入特征图
    hipMalloc((void**)&pWeight_device, k*c*r*s*sizeof(_Float16));    // GPU卷积核权重
    hipMalloc((void**)&pOut_device, n*k*outh*outw*sizeof(_Float16)); // GPU输出特征图

    // ==================== 数据初始化 ====================
    // 初始化输入特征图：随机值范围[0, 1)
    for(int i = 0; i < n*c*h*w; i++)
    {
        pIn[i] = (rand()%255)/255.0;  // 生成[0, 1)范围的随机数
    }

    // 初始化卷积核权重：随机值范围[0, 1)
    for(int i = 0; i < k*c*r*s; i++)
    {
        pWeight[i] = (rand()%255)/255.0;  // 生成[0, 1)范围的随机数
    }

    // 初始化输出缓冲区为0
    for(int i = 0; i < n*k*outh*outw; i++)
    {
        pOut[i] = 0.0;       // CPU计算结果初始化
        pOut_host[i] = 0.0;  // GPU计算结果初始化
    }

    // ==================== 数据传输：主机到设备 ====================
    hipMemcpy(pIn_device, pIn, n*c*h*w*sizeof(_Float16), hipMemcpyHostToDevice);        // 传输输入数据
    hipMemcpy(pWeight_device, pWeight, k*c*r*s*sizeof(_Float16), hipMemcpyHostToDevice); // 传输权重数据
    hipMemcpy(pOut_device, pOut, n*k*outh*outw*sizeof(_Float16), hipMemcpyHostToDevice); // 传输输出缓冲区

    // ==================== GPU内核配置 ====================

    // 步骤1：初始化卷积问题描述结构体
    problem_t problem;
    int paramSize;
    kernelInfo_t kernelInfo;

    // 设置卷积问题的输入输出指针（GPU设备端地址）
    problem.in        = pIn_device;        // GPU输入特征图地址
    problem.weight    = pWeight_device;    // GPU卷积核权重地址
    problem.out       = pOut_device;       // GPU输出特征图地址

    // 设置卷积操作参数
    problem.n         = n;   // 批处理大小
    problem.c         = c;   // 输入通道数
    problem.h         = h;   // 输入特征图高度
    problem.w         = w;   // 输入特征图宽度
    problem.k         = k;   // 输出通道数
    problem.r         = r;   // 卷积核高度
    problem.s         = s;   // 卷积核宽度
    problem.u         = u;   // 高度方向步长
    problem.v         = v;   // 宽度方向步长
    problem.p         = p;   // 高度方向填充
    problem.q         = q;   // 宽度方向填充

    // 步骤2：获取内核参数大小并分配内存
    getParamsize(&problem, &paramSize);
    printf("paramsize:%d\n", paramSize);  // 打印参数结构体大小
    void* param = malloc(paramSize);       // 分配内核参数内存

    // 步骤3：配置GPU内核执行参数
    getkernelInfo(&problem, &kernelInfo, param);

    // 配置GPU执行网格和线程块
    dim3 groups(kernelInfo.blockx, kernelInfo.blocky, kernelInfo.blockz);    // Grid维度
    dim3 threads(kernelInfo.threadx, kernelInfo.thready, kernelInfo.threadz); // Block维度
    int ldsSize = kernelInfo.dynmicLdsSize;  // 动态共享内存大小

    // ==================== GPU内核执行和性能测试 ====================

    // 预热执行：确保GPU初始化完成，获得稳定的性能测量
    hipExtLaunchKernel(kernelInfo.kernelPtr, groups, threads, (void**)&param, ldsSize, 0, 0, 0, 0);

    // 将GPU计算结果传输回主机端
    hipMemcpy(pOut_host, pOut_device, n*k*outh*outw*sizeof(_Float16), hipMemcpyDeviceToHost);

    // ==================== 性能基准测试 ====================
    // 创建HIP事件用于精确计时
    hipEvent_t start, stop;
    hipEventCreate(&start);
    hipEventCreate(&stop);
    hipEventRecord(start, 0);  // 记录开始时间
    float time_elapsed = 0.0;

    // 执行多次迭代以获得平均性能
    int iternum = 100;  // 迭代次数
    for(int i = 0; i < iternum; i++)
    {
        // 启动GPU内核
        hipExtLaunchKernel(kernelInfo.kernelPtr, groups, threads, (void**)&param, ldsSize, 0, 0, 0, 0);
    }
    hipEventRecord(stop, 0);  // 记录结束时间

    // 等待GPU执行完成并计算执行时间
    hipEventSynchronize(stop);
    hipEventElapsedTime(&time_elapsed, start, stop);

    // 打印平均执行时间（微秒）
    printf("time: %f us\n", time_elapsed*1000/iternum);

    // 清理HIP事件资源
    hipEventDestroy(start);
    hipEventDestroy(stop);

    // 释放内核参数内存
    free(param);

    // ==================== 正确性验证 ====================
    printf("===================start verfiy===================\n");

    // 使用CPU参考实现计算卷积结果
    conv2dcpu(pIn, pWeight, pOut, n, c, h, w, k, r, s, u, v, p, q);

    // 比较GPU和CPU计算结果
    int error = 0;
    for(int i = 0; i < n*k*outh*outw; i++)
    {
        float device_out = pOut_host[i];  // GPU计算结果
        float cpu_out = pOut[i];          // CPU计算结果

        // 计算相对误差并检查数值有效性
        // 注意：原代码存在除零错误风险，当pOut_host[i]=0时会出现问题
        // 建议的修复：使用绝对误差或改进的相对误差计算
        if((fabs(pOut_host[i] - pOut[i]))/pOut_host[i] > 0.01 || isnan(device_out) || isinf(device_out))
        {
            printf("error, postion:%d, gpuvalue:%f, cpuvalue:%f\n", i, (float)pOut_host[i], (float)pOut[i]);
            error++;
            break;  // 发现第一个错误就退出
        }
    }
    printf("================finish,error:%d=========================\n", error);

    // ==================== 资源清理 ====================
    // 释放GPU设备端内存
    hipFree(pIn_device);
    hipFree(pWeight_device);
    hipFree(pOut_device);

    // 释放主机端内存
    free(pIn);
    free(pWeight);
    free(pOut);
    free(pOut_host);

    return 0;  // 程序正常结束
}