# 2D卷积GPU优化项目参数说明文档

## 卷积操作参数定义

本项目使用以下参数命名约定来描述2D卷积操作的各项配置：

### 基本维度参数
    int n;    // batch size - 批处理大小，一次处理的样本数量
    int c;    // input channel number - 输入通道数，如RGB图像为3通道
    int h;    // input height - 输入特征图高度（像素）
    int w;    // input width - 输入特征图宽度（像素）
    int k;    // kernel count - 卷积核数量，等于输出通道数
    int r;    // kernel height - 卷积核高度（像素）
    int s;    // kernel width - 卷积核宽度（像素）

### 卷积操作参数
    int u;    // stride height - 卷积在高度方向上的步长，控制输出尺寸
    int v;    // stride width - 卷积在宽度方向上的步长，控制输出尺寸
    int p;    // padding height - 卷积在高度方向上的填充，扩展输入边界
    int q;    // padding width - 卷积在宽度方向上的填充，扩展输入边界

### 输出维度参数
    int Oh;   // output height - 卷积在高度方向上的输出大小
    int Ow;   // output width - 卷积在宽度方向上的输出大小

## 输出尺寸计算公式

根据卷积操作的数学定义，输出特征图尺寸计算公式为：

    Oh = (int)((h - r + 2*p) / u) + 1;    // 输出高度计算
    Ow = (int)((w - s + 2*q) / v) + 1;    // 输出宽度计算

公式说明：
- (h - r + 2*p): 考虑卷积核大小和填充后的有效输入高度
- 除以u: 应用步长的降采样效果
- +1: 卷积操作的标准公式修正项

## 数据布局格式

本项目严格遵循以下数据布局约定（比赛要求）：

### 输入数据布局：NCHW格式
    维度顺序：[N, C, H, W]
    - N: batch维度，不同样本
    - C: channel维度，不同通道（如RGB）
    - H: height维度，图像高度
    - W: width维度，图像宽度

    线性索引：input[n*C*H*W + c*H*W + h*W + w]

### 权值数据布局：KCRS格式
    维度顺序：[K, C, R, S]
    - K: kernel维度，不同卷积核
    - C: channel维度，输入通道
    - R: kernel height维度，卷积核高度
    - S: kernel width维度，卷积核宽度

    线性索引：weight[k*C*R*S + c*R*S + r*S + s]

### 输出数据布局：NKOhOw格式
    维度顺序：[N, K, Oh, Ow]
    - N: batch维度，不同样本
    - K: kernel维度，输出通道
    - Oh: output height维度，输出高度
    - Ow: output width维度，输出宽度

    线性索引：output[n*K*Oh*Ow + k*Oh*Ow + oh*Ow + ow]

## 实现要求

请在conv2d.cpp文件中实现相关优化函数，要求：

1. **接口兼容性**: 严格遵循给定的函数接口，不得修改函数签名
2. **数据格式**: 必须支持上述NCHW/KCRS/NKOhOw数据布局格式
3. **性能优化**: 充分利用GPU并行计算能力，实现高性能卷积计算
4. **正确性**: 确保计算结果与CPU参考实现一致（误差<1%）
5. **通用性**: 支持任意合理的卷积参数配置组合