/*
 * 2D卷积GPU基础实现 - 直接卷积算法
 *
 * 本文件实现了基础的2D卷积GPU内核，采用直接卷积算法
 * 适合学习GPU编程基础概念，但性能相对较低
 *
 * 实现特点：
 * 1. 直接卷积算法 - 每个线程计算一个输出元素
 * 2. 简单的线程映射策略
 * 3. 全局内存访问模式
 * 4. 基础的边界处理
 */

#include <hip/hip_runtime.h>  // HIP运行时API
#include "conv2d.h"           // 卷积相关数据结构定义

// ==================== 内核参数结构体定义 ====================
/**
 * GPU内核参数结构体（基础版本）
 * 包含卷积操作所需的所有参数和预留字段
 */
typedef struct mykernelParamType
{
    // 数据指针
    _Float16*   pin;      // 输入特征图数据地址
    _Float16*   pweight;  // 卷积核权重数据地址
    _Float16*   pout;     // 输出特征图数据地址

    // 卷积操作参数
    unsigned int n;       // 批处理大小 (batch size)
    unsigned int c;       // 输入通道数 (input channel number)
    unsigned int h;       // 输入特征图高度 (input height)
    unsigned int w;       // 输入特征图宽度 (input width)
    unsigned int k;       // 输出通道数/卷积核数量 (kernel count)
    unsigned int r;       // 卷积核高度 (kernel height)
    unsigned int s;       // 卷积核宽度 (kernel width)
    unsigned int u;       // 高度方向步长 (stride height)
    unsigned int v;       // 宽度方向步长 (stride width)
    unsigned int p;       // 高度方向填充 (padding height)
    unsigned int q;       // 宽度方向填充 (padding width)
    unsigned int Oh;      // 输出特征图高度 (output height)
    unsigned int Ow;      // 输出特征图宽度 (output width)

    // 预留字段（用于未来扩展）
    unsigned int revs0;   // 预留字段0
    unsigned int revs1;   // 预留字段1
    unsigned int revs2;   // 预留字段2
    unsigned int revs3;   // 预留字段3
    unsigned int revs4;   // 预留字段4
    unsigned int revs5;   // 预留字段5
    unsigned int revs6;   // 预留字段6
    unsigned int revs7;   // 预留字段7
} mykernelParamType;

// ==================== 主GPU内核函数（基础版本）====================
/**
 * 直接卷积算法GPU内核函数
 *
 * 算法原理：
 * 1. 每个线程负责计算输出特征图的一个元素
 * 2. 通过三重嵌套循环遍历卷积核窗口
 * 3. 对每个输出位置执行完整的卷积计算
 * 4. 处理边界填充和步长
 *
 * 线程映射策略：
 * - X维度：输出空间位置 (Oh×Ow)
 * - Y维度：输出通道 (k)
 * - Z维度：批处理 (n)
 *
 * @param param 卷积参数结构体
 */
extern "C" __global__ void myKernelConv2dGpu(mykernelParamType param)
    __attribute__((amdgpu_flat_work_group_size(1,256)))  // AMD GPU优化属性
{
    // ==================== 线程索引计算 ====================
    int x = blockIdx.x * blockDim.x + threadIdx.x;  // 输出空间位置索引
    int y = blockIdx.y * blockDim.y + threadIdx.y;  // 输出通道索引
    int z = blockIdx.z;                             // 批处理索引

    // ==================== 边界检查 ====================
    // 确保线程索引在有效范围内
    if(x >= param.Oh*param.Ow || y >= param.k || z >= param.n)
    {
        return;  // 超出范围的线程直接返回
    }

    // ==================== 输出位置解码 ====================
    // 将一维空间索引x解码为二维坐标(posOh, posOw)
    int posOh = x / param.Ow;  // 输出特征图高度坐标
    int posOw = x % param.Ow;  // 输出特征图宽度坐标

    // ==================== 输入起始位置计算 ====================
    // 根据步长和填充计算对应的输入特征图起始位置
    int posh_ori = posOh * param.u - param.p;  // 输入高度起始坐标（考虑步长和填充）
    int posw_ori = posOw * param.v - param.q;  // 输入宽度起始坐标（考虑步长和填充）

    // ==================== 累加器初始化 ====================
    float sum = 0.0;  // 卷积结果累加器

    // ==================== 内存偏移量预计算 ====================
    // 预计算各种内存访问的基础偏移量，提高访问效率
    int inOffset = z*param.c*param.h*param.w + posh_ori*param.w + posw_ori;  // 输入基础偏移
    int weiOffset = y*param.c*param.r*param.s;                               // 权重基础偏移
    int inChannelOffset = param.h*param.w;                                   // 输入通道间偏移
    int weightChannelOffset = param.r*param.s;                               // 权重通道间偏移

    // ==================== 卷积核窗口遍历 ====================
    // 三重嵌套循环：卷积核高度 × 卷积核宽度 × 输入通道
    for(int i = 0; i < param.r; i++)  // 遍历卷积核高度
    {
        for(int j = 0; j < param.s; j++)  // 遍历卷积核宽度
        {
            // 计算当前卷积核位置对应的输入坐标
            int posh_real = posh_ori + i;  // 实际输入高度坐标
            int posw_real = posw_ori + j;  // 实际输入宽度坐标

            // ==================== 边界检查 ====================
            // 只处理在有效输入范围内的位置（实现padding效果）
            if(posh_real >= 0 && posw_real >= 0 && posw_real < param.w && posh_real < param.h)
            {
                // 初始化当前卷积核位置的内存偏移量
                int inOffsetTmp = inOffset;      // 输入数据临时偏移
                int weiOffsetTmp = weiOffset;    // 权重数据临时偏移

                // ==================== 通道维度遍历 ====================
                // 对所有输入通道进行卷积计算
                for(int channel = 0; channel < param.c; channel++)
                {
                    // 执行卷积乘积累加：input × weight
                    // 注意：这里的内存访问模式可能不是最优的
                    sum += (float)(param.pin[inOffsetTmp + i*param.w + j] *
                                  param.pweight[weiOffsetTmp + i*param.s + j]);

                    // 移动到下一个输入通道
                    inOffsetTmp += inChannelOffset;
                    weiOffsetTmp += weightChannelOffset;
                }
            }
            // 超出边界的位置自动视为0（padding效果）
        }
    }

    // ==================== 结果写回 ====================
    // 计算输出特征图的线性索引并写入结果
    int outOffset = z*param.k*param.Oh*param.Ow + y*param.Oh*param.Ow + x;
    param.pout[outOffset] = (_Float16)sum;  // 转换为半精度并写入

} // 内核函数结束


// ==================== 主机端接口函数（基础版本）====================

/**
 * 获取内核参数结构体大小
 *
 * @param problem 输入：卷积问题描述结构体指针
 * @param paramSize 输出：参数结构体大小（字节）
 * @return 0表示成功
 */
int getParamsize(__in__ problem_t* problem, __out__ int* paramSize)
{
    *paramSize = sizeof(mykernelParamType);
    return 0;
}

/**
 * 配置GPU内核执行参数（基础版本）
 *
 * 配置基础的线程映射策略：
 * - X维度：处理输出空间位置，每16个线程为一组
 * - Y维度：处理输出通道，每16个线程为一组
 * - Z维度：处理批处理，每个batch一个线程块
 *
 * @param problem 输入：卷积问题描述结构体指针
 * @param kernelInfo 输出：内核执行配置信息
 * @param param 输入输出：内核参数结构体指针
 * @return 0表示成功
 */
int getkernelInfo(__in__ problem_t* problem, __out__  kernelInfo_t* kernelInfo, __in_out__ void* param)
{
    // 类型转换为具体的参数结构体
    mykernelParamType* pArgs = (mykernelParamType*)param;

    // ==================== 提取卷积参数 ====================
    unsigned int n = problem->n;  // 批处理大小
    unsigned int c = problem->c;  // 输入通道数
    unsigned int h = problem->h;  // 输入高度
    unsigned int w = problem->w;  // 输入宽度
    unsigned int k = problem->k;  // 输出通道数
    unsigned int r = problem->r;  // 卷积核高度
    unsigned int s = problem->s;  // 卷积核宽度
    unsigned int u = problem->u;  // 高度步长
    unsigned int v = problem->v;  // 宽度步长
    unsigned int p = problem->p;  // 高度填充
    unsigned int q = problem->q;  // 宽度填充

    // ==================== 计算输出尺寸 ====================
    unsigned int outh = (h - r + 2*p)/u + 1;  // 输出高度
    unsigned int outw = (w - s + 2*q)/v + 1;  // 输出宽度

    // ==================== GPU执行配置计算 ====================
    // 基础的线程块配置：16×16=256个线程每块
    kernelInfo->blockx   = (outh*outw + 15)/16;  // X维度块数：处理输出空间，向上取整到16的倍数
    kernelInfo->blocky   = (k + 15)/16;          // Y维度块数：处理输出通道，向上取整到16的倍数
    kernelInfo->blockz   = n;                    // Z维度块数：每个batch一个块
    kernelInfo->threadx  = 16;                   // X维度线程数：16个线程
    kernelInfo->thready  = 16;                   // Y维度线程数：16个线程
    kernelInfo->threadz  = 1;                    // Z维度线程数：1个线程
    kernelInfo->dynmicLdsSize = 0;               // 不使用动态共享内存
    kernelInfo->kernelPtr = (void*)myKernelConv2dGpu;  // 内核函数指针

    // ==================== 初始化内核参数结构体 ====================
    pArgs->pin = problem->in;       // 输入特征图指针
    pArgs->pweight = problem->weight; // 卷积核权重指针
    pArgs->pout = problem->out;     // 输出特征图指针
    pArgs->n = n;                   // 批处理大小
    pArgs->c = c;                   // 输入通道数
    pArgs->h = h;                   // 输入高度
    pArgs->w = w;                   // 输入宽度
    pArgs->k = k;                   // 输出通道数
    pArgs->r = r;                   // 卷积核高度
    pArgs->s = s;                   // 卷积核宽度
    pArgs->u = u;                   // 高度步长
    pArgs->v = v;                   // 宽度步长
    pArgs->p = p;                   // 高度填充
    pArgs->q = q;                   // 宽度填充
    pArgs->Oh = outh;               // 输出高度
    pArgs->Ow = outw;               // 输出宽度

    return 0;
}