#!/bin/bash
#
# 2D卷积GPU优化项目SLURM作业提交脚本
#
# 本脚本用于在SLURM集群管理系统上提交GPU计算作业
# 执行多个不同配置的卷积性能测试用例
#

# ==================== SLURM作业配置 ====================
#SBATCH -J test                         # 作业名称
#SBATCH -p kshdnormal                   # 队列名称（使用whichpartition命令查看可用队列）
#SBATCH -N 1                            # 申请节点数量：1个计算节点
#SBATCH --ntasks-per-node=1             # 每个节点运行的进程数：1个进程
#SBATCH -c 8                            # 每个进程使用的CPU核心数：8核
#SBATCH --gres=dcu:1                    # 每个节点申请的DCU(GPU)数量：1个DCU
#SBATCH -o %j.out                       # 作业标准输出文件：作业ID.out
#SBATCH -e %j.out                       # 作业错误输出文件：与标准输出合并

# ==================== 环境模块配置 ====================
# 显示当前已加载的模块
module list

# 加载必需的软件环境模块
module load anaconda3/5.2.0              # Python环境
module load compiler/devtoolset/7.3.1    # GCC编译器工具集
module load mpi/hpcx/gcc-7.3.1          # MPI并行计算库
module load compiler/dtk/25.04           # DCU开发工具包

# 再次显示模块加载状态，确认环境配置正确
module list

# ==================== 项目构建 ====================
# 切换到项目目录（需要根据实际路径修改）
cd /public/home/<USER>/1/

# 清理之前的构建文件
make clean

# 重新编译项目
make

# ==================== 性能测试用例执行 ====================
# 执行多个不同配置的卷积测试用例，覆盖各种典型应用场景
# 参数格式：./conv2dfp16demo n c h w k r s u v p q

# 测试用例1：大batch，小通道，中等分辨率
# ResNet风格：batch=128, channels=3->32, 225x225输入, 3x3卷积, stride=2
./conv2dfp16demo  128 3   225 225 32  3   3   2   2   0   0

# 测试用例2：中等batch，大通道，小分辨率
# 深层网络：batch=49, channels=128->384, 35x35输入, 3x3卷积, stride=2
./conv2dfp16demo  49  128 35  35  384 3   3   2   2   0   0

# 测试用例3：小batch，大通道，中等分辨率
# 特征提取：batch=16, channels=128->256, 105x105输入, 3x3卷积, stride=2
./conv2dfp16demo  16  128 105 105 256 3   3   2   2   0   0

# 测试用例4：大batch，小通道，大分辨率，大卷积核
# 初始层：batch=128, channels=3->64, 230x230输入, 7x7卷积, stride=2
./conv2dfp16demo  128 3   230 230 64  7   7   2   2   0   0

# 测试用例5：极小batch，小通道，超大分辨率
# 高分辨率处理：batch=2, channels=3->64, 838x1350输入, 7x7卷积, stride=2
./conv2dfp16demo  2   3   838 1350    64  7   7   2   2   0   0

# 测试用例6：大batch，大通道，小分辨率，小卷积核
# 密集计算：batch=256, channels=256->256, 28x28输入, 2x2卷积, stride=2
./conv2dfp16demo  256 256 28  28  256 2   2   2   2   0   0