# 卷积算子优化竞赛项目

## 1. 赛题背景与项目意义

### 1.1 卷积神经网络的重要性
卷积神经网络(Convolutional Neural Network, CNN)是一类常用的深度学习模型，在计算机视觉、自然语言处理等领域广泛应用。卷积算子是CNN的重要组成部分，用于提取图像、文本等数据中的特征。

### 1.2 卷积运算的计算挑战
卷积运算是深度学习中常用的操作之一，但是由于计算量较大，在大型神经网络中可能成为性能瓶颈。导致训练时间过长，因此，为了提高卷积运算的计算效率，需要通过进行编程技术手段进行优化。

### 1.3 比赛目标与技术要求
本比赛要求参赛者优化卷积算子的并行编程实现，具体而言，给定一个输入张量和一个卷积核张量，要求实现一个高效的卷积运算，并输出卷积结果。卷积(Conv)算子的DCU加速优化，希望借助现有编程能够帮助更多选手了解DCU加速卡和DCU加速编程。

### 1.4 卷积运算数学定义
本赛题中，卷积(Conv)算子的形式如下：
**C = A @ B**

其中：
- **A**: 输入张量，维度信息为(n, c, h, w)，n为卷积输入张量的batchSize，c为卷积输入张量的通道数
- **B**: 卷积核张量，维度信息为(k, c, r, s)，k为卷积核张量的个数，c为卷积核通道数
- **C**: 输出张量，维度信息为(n, k, oh, ow)，其中卷积输出张量的维度信息为(n, k, oh, ow)

### 1.5 卷积参数说明
对于卷积运算的三个张量A、B、C来说，其各个维度关系如下表：
1. **A(n)=C(n)**: 输入张量的batchSize与输出张量的batchsize一致
2. **A(c)=B(c)**: 输入张量的通道数与卷积核张量的通道数必须一致
3. **B(k)=C(k)**: 即输出张量的通道数，是由卷积核的个数(k)来决定的
4. **oh=(h+2*p)/u + 1, ow=(w+2*q)/v + 1**: 输出尺寸计算公式
5. **输出张量内的任一点的数值计算方式为卷积核与对应的输入位置数据的乘积累加**

### 1.6 项目技术特点
本次比赛采用异构编程加速卷积算法，要求在异构硬件卡上实现相关算法进行优化。输入张量A、卷积核张量B以及输出张量C均采用通道交错(NCHW)存储格式；选手实现的相关kernel，必须基于该存储格式；否则，验证程序无法验证通过。

## 2. 项目结构分析

### 2.1 整体架构
```
项目根目录/
├── include/
│   ├── conv2d.h        # 核心数据结构定义
│   └── verfiy.h        # CPU参考实现
├── src/
│   ├── conv2d.cpp      # GPU kernel实现
│   └── main.cpp        # 主程序和性能测试
├── Makefile            # 构建配置
└── readme.txt          # 参数说明
```

### 2.2 核心数据结构

#### 2.2.1 卷积问题描述结构体
```cpp
typedef struct {
    _Float16*   in;          // 输入数据地址
    _Float16*   weight;      // 权值数据地址  
    _Float16*   out;         // 输出数据地址
    unsigned int n;          // batch size
    unsigned int c;          // channel number
    unsigned int h, w;       // 输入数据高度和宽度
    unsigned int k;          // 卷积核数量
    unsigned int r, s;       // 卷积核高度和宽度
    unsigned int u, v;       // 步长(stride)
    unsigned int p, q;       // 填充(padding)
} problem_t;
```

#### 2.2.2 GPU执行配置结构体
```cpp
typedef struct {
    unsigned int blockx, blocky, blockz;    // Grid维度
    unsigned int threadx, thready, threadz; // Block维度
    unsigned int dynmicLdsSize;             // 动态共享内存大小
    void* kernelPtr;                        // Kernel函数指针
} kernelInfo_t;
```

## 3. 卷积运算数学原理

### 3.1 2D卷积数学定义
对于输入特征图 $X \in \mathbb{R}^{C \times H \times W}$ 和卷积核 $W \in \mathbb{R}^{K \times C \times R \times S}$，输出特征图 $Y \in \mathbb{R}^{K \times O_h \times O_w}$ 的计算公式为：

$$Y_{k,i,j} = \sum_{c=0}^{C-1} \sum_{r=0}^{R-1} \sum_{s=0}^{S-1} X_{c,i \cdot u + r - p, j \cdot v + s - q} \cdot W_{k,c,r,s}$$

### 3.2 输出尺寸计算
```cpp
// 输出高度和宽度计算公式
Oh = (h - r + 2*p) / u + 1;
Ow = (w - s + 2*q) / v + 1;
```

### 3.3 数据布局规范
- **输入数据**: NCHW格式 (Batch, Channel, Height, Width)
- **权重数据**: KCRS格式 (Kernel_num, Channel, Kernel_height, Kernel_width)  
- **输出数据**: NKOhOw格式 (Batch, Kernel_num, Output_height, Output_width)

## 4. GPU Kernel实现分析

### 4.1 线程映射策略
```cpp
extern "C" __global__ void myKernelConv2dGpu(mykernelParamType param) {
    int x = blockIdx.x * blockDim.x + threadIdx.x;  // 输出位置索引
    int y = blockIdx.y * blockDim.y + threadIdx.y;  // 输出通道索引
    int z = blockIdx.z;                             // batch索引
    
    // 边界检查
    if(x >= param.Oh*param.Ow || y >= param.k || z >= param.n) {
        return;
    }
}
```

### 4.2 内存访问模式
```cpp
// 计算输入和权重的内存偏移
int inOffset = z*param.c*param.h*param.w + posh_ori*param.w + posw_ori;
int weiOffset = y*param.c*param.r*param.s;
int inChannelOffset = param.h*param.w;
int weightChannelOffset = param.r*param.s;
```

### 4.3 卷积计算核心循环
```cpp
for(int i = 0; i < param.r; i++) {
    for(int j = 0; j < param.s; j++) {
        // 边界检查和有效性验证
        if(posh_real>=0 && posw_real>=0 && posw_real<param.w && posh_real<param.h) {
            // 跨通道累加
            for(int channel = 0; channel<param.c; channel++) {
                sum += (float)(param.pin[inOffsetTmp + i*param.w + j] * 
                              param.pweight[weiOffsetTmp + i*param.s + j]);
            }
        }
    }
}
```

## 5. 性能测试与验证框架

### 5.1 性能测试方法
```cpp
// 预热执行
hipExtLaunchKernel(kernelInfo.kernelPtr, groups, threads, 
                   (void**)&param, ldsSize, 0, 0, 0, 0);

// 计时测试
hipEvent_t start, stop;
int iternum = 100;
for(int i=0; i<iternum; i++) {
    hipExtLaunchKernel(kernelInfo.kernelPtr, groups, threads, 
                       (void**)&param, ldsSize, 0, 0, 0, 0);
}
hipEventElapsedTime(&time_elapsed, start, stop);
printf("time: %f us\n", time_elapsed*1000/iternum);
```

### 5.2 正确性验证
```cpp
// CPU参考实现对比
conv2dcpu(pIn, pWeight, pOut, n, c, h, w, k, r, s, u, v, p, q);

// 误差检查
for(int i=0; i<n*k*outh*outw; i++) {
    if((fabs(pOut_host[i] - pOut[i]))/pOut_host[i] > 0.01 || 
       isnan(device_out) || isinf(device_out)) {
        printf("error, position:%d, gpuvalue:%f, cpuvalue:%f\n", 
               i, (float)pOut_host[i], (float)pOut[i]);
        error++;
        break;
    }
}
```

## 6. 当前实现特点分析

### 6.1 实现优势
- **接口规范**: 严格遵循比赛要求的接口规范，保证兼容性
- **通用性强**: 支持任意卷积参数配置(n,c,h,w,k,r,s,u,v,p,q)
- **精度保证**: 包含完整的正确性验证机制，误差控制在1%以内
- **NCHW格式**: 正确实现了比赛要求的通道交错存储格式
- **边界处理**: 正确处理padding和边界条件，支持任意步长

### 6.2 性能瓶颈分析
- **内存访问效率**: 全局内存访问模式存在优化空间，缺乏数据局部性利用
- **计算资源利用**: 未充分利用GPU的向量化计算能力和并行度
- **数据重用**: 缺乏共享内存(LDS)优化策略，数据重用率低
- **线程配置**: 固定的16x16配置未针对不同问题规模进行自适应调整
- **访存与计算重叠**: 缺乏流水线优化，访存延迟未被有效隐藏

### 6.3 比赛约束下的挑战
- **接口限制**: 不能修改main.cpp和头文件，优化空间受限
- **第三方库限制**: 不能使用现有的高性能库，需要从零实现
- **存储格式固定**: 必须使用NCHW格式，不能采用其他更优的数据布局
- **内存分配限制**: 临时存储空间需要合理控制，避免内存占用过多

## 7. 比赛约束下的优化策略

### 7.1 接口约束下的优化方向
在比赛规则限制下，优化策略需要在保持接口不变的前提下进行：

#### 7.1.1 Kernel参数结构体优化
```cpp
// 可以修改mykernelParamType结构体内容
typedef struct mykernelParamType {
    _Float16* pin;           // 输入数据地址
    _Float16* pweight;       // 权值数据地址
    _Float16* pout;          // 输出数据地址
    // 可以增加优化相关的参数
    unsigned int tile_size;  // 分块大小
    unsigned int unroll_factor; // 循环展开因子
    // 预留字段可用于优化参数
    unsigned int revs0;      // 可用于缓存策略参数
    unsigned int revs1;      // 可用于向量化参数
} mykernelParamType;
```

### 7.2 内存层次优化策略
- **共享内存利用**: 将频繁访问的输入数据和权重缓存到LDS(Local Data Share)
- **寄存器优化**: 优化寄存器使用，减少寄存器溢出
- **内存合并访问**: 优化内存访问模式，提高带宽利用率
- **数据预取**: 使用异步内存操作隐藏访存延迟

### 7.3 计算优化策略
- **向量化操作**: 利用GPU的SIMD指令集进行向量化计算
- **循环展开**: 减少循环开销，提高指令级并行度
- **数据重用**: 最大化输入数据和权重的重用率
- **计算与访存重叠**: 通过流水线技术隐藏访存延迟

### 7.4 线程配置优化
- **Work-group大小调优**: 根据硬件特性选择最优的线程块大小
- **Wavefront利用率**: 针对AMD GPU的wavefront特性进行优化
- **负载均衡**: 确保各个计算单元的负载均衡

### 7.5 算法层面优化
- **分块策略**: 针对大尺寸输入进行合理的分块处理
- **数据布局优化**: 在NCHW约束下优化数据访问模式
- **边界处理优化**: 高效处理padding和边界条件

## 8. 技术创新点

### 8.1 半精度计算优化
- **混合精度策略**: 在保证精度的前提下最大化性能
- **数值稳定性**: 处理半精度计算中的数值问题

### 8.2 自适应配置
- **动态参数调整**: 根据输入规模自动调整执行配置
- **性能模型**: 建立性能预测模型指导优化

## 9. 项目价值与影响

### 9.1 学术价值
- **GPU计算优化**: 为GPU并行计算优化提供实践案例
- **算法实现**: 展示了从数学公式到高效GPU实现的完整过程

### 9.2 工程价值  
- **性能基准**: 为AMD GPU上的卷积实现提供性能基准
- **代码复用**: 提供了可扩展的GPU卷积实现框架

### 9.3 生态贡献
- **开源贡献**: 为AMD GPU生态系统贡献高质量代码
- **技术推广**: 推动HIP编程模型的应用和发展

## 10. 比赛评测说明与运行结果分析

### 10.1 比赛评测要求
参赛队伍需要在比赛的截止日期前完成提交。需要确保如下赛题要求：

#### 10.1.1 核心优化要求
1) **性能优化**: 参赛队在给定接口函数的基础上进行卷积性能优化，给定测试代码中，示例代码中给出以伪随机数生成张量作为测试数据，验证算法的性能与正确性，选手可以适当调整main.cpp内的相关打印语句，来调试程序，但最终提交的代码中，需要与原始代码一致。

2) **函数接口保持**: 参赛队需根据给出的demo程序对conv2d.cpp中的函数体进行具体实现与优化。选手可以根据各自核函数入参需求，增改mykernelParamType结构体内的成员，但需要注意：conv2d.cpp内各个函数接口请不要改动，以防止main函数内无法正常调用选手优化的kernel。

#### 10.1.2 技术限制条件
3) **内存管理**: 算法实现中如需分配临时存储空间，需要在函数内正确的处理空间分配与释放，参数选手需选取合理的算法，避免占用过多内存。

4) **数据格式要求**: 本次比赛输入张量A、卷积核张量B以及输出张量C均采用通道交错(NCHW)存储格式；选手实现的相关kernel，必须基于该存储格式；否则，验证程序无法验证通过。

5) **第三方库限制**: 参赛队不能调用第三方库的实现来加速自己的kernel计算。

6) **代码完整性**: 禁止修改main.cpp和verify.h、conv2d.h等辅助与记时函数，以及相关接口；仅需在conv2d.cpp内做优化，保证程序正确编译运行。

### 10.2 测试环境与配置
7) **编译要求**: 参数队需提供完整的源码，供赛事组进行评测，尽量保证只修改demo中的conv2d.cpp文件；源码编译方式采用demo中给定的Makefile编译参数，不得修改。

8) **硬件环境**: 移植过程中限制使用一块类GPU卡+8CPU核心，测试使用numactl限制在一个NUMA node上运行。

### 10.3 性能评估标准
9) **提交次数限制**: 参赛队比赛期间最多可提交3次结果，赛事组以最后提交的结果为准。

10) **学术诚信**: 不得抄袭其他参赛队的答案，一经发现成绩作废。

### 10.4 性能指标分析
项目通过以下关键指标评估性能：
- **执行时间**: 单次卷积操作的平均执行时间（微秒）
- **吞吐量**: 每秒处理的数据量（GFLOPS）
- **内存带宽利用率**: 实际内存带宽与理论峰值的比率
- **正确性验证**: 与CPU参考实现的误差控制在1%以内

### 10.5 测试配置示例
```bash
# 典型测试参数
./conv2dfp16demo 1 32 32 32 32 3 3 1 1 1 1
# 参数含义: n c h w k r s u v p q
# batch=1, channels=32, input=32x32, kernels=32, kernel_size=3x3, stride=1, padding=1
```

### 10.6 性能分析方法
```cpp
// 计算理论FLOPS
long long total_ops = (long long)n * k * outh * outw * c * r * s * 2; // 乘加操作
double gflops = (double)total_ops / (time_elapsed * 1e-6) / 1e9;

// 内存带宽分析
long long memory_access = (long long)(n*c*h*w + k*c*r*s + n*k*outh*outw) * sizeof(_Float16);
double bandwidth_gb_s = (double)memory_access / (time_elapsed * 1e-6) / 1e9;
```

## 11. 编译与部署

### 11.1 环境要求
```makefile
# Makefile配置分析
CC=$(HIP_PATH)/bin/hipcc                    # HIP编译器
CXXFLAGS += -DHIP_ROCM -DNDEBUG            # 编译选项
--offload-arch=gfx906                      # 目标GPU架构
INCLUDES += -I$(HIP_PATH)/include          # 头文件路径
```

### 11.2 构建流程
```bash
# 清理构建
make clean

# 编译项目
make all

# 运行测试
./conv2dfp16demo 1 32 32 32 32 3 3 1 1 1 1
```

### 11.3 依赖关系
- **ROCm平台**: AMD GPU计算平台
- **HIP运行时**: GPU编程接口
- **数学库**: 半精度浮点运算支持

## 12. 代码质量与可维护性

### 12.1 代码结构设计
- **模块化设计**: 清晰的接口分离和功能模块划分
- **参数化配置**: 支持灵活的卷积参数配置
- **错误处理**: 完善的边界检查和错误处理机制

### 12.2 可扩展性
```cpp
// 扩展接口设计
typedef struct {
    unsigned int revs0;  // 预留字段，支持未来功能扩展
    unsigned int revs1;  // 可用于新的优化参数
    // ... 更多预留字段
} mykernelParamType;
```

### 12.3 测试覆盖
- **功能测试**: CPU参考实现对比验证
- **性能测试**: 多轮迭代的性能基准测试
- **边界测试**: 各种参数组合的边界条件测试

## 13. 技术挑战与解决方案

### 13.1 内存访问优化挑战
**挑战**: GPU全局内存访问延迟高，带宽利用率低
**解决方案**:
- 实现数据预取和缓存策略
- 优化内存访问模式，实现合并访问
- 利用共享内存减少全局内存访问次数

### 13.2 数值精度挑战
**挑战**: 半精度计算可能导致精度损失和数值不稳定
**解决方案**:
- 实现混合精度计算策略
- 添加数值稳定性检查
- 优化累加顺序减少舍入误差

### 13.3 性能可移植性挑战
**挑战**: 不同GPU架构的性能特性差异
**解决方案**:
- 实现自适应参数调整机制
- 建立性能模型指导优化
- 提供多种优化策略的运行时选择

## 14. 比赛策略与发展路线

### 14.1 比赛阶段优化策略
#### 14.1.1 第一阶段：基础优化（提交1）
- **内存访问优化**: 实现合并访问，提高内存带宽利用率
- **共享内存利用**: 将热点数据缓存到LDS，减少全局内存访问
- **线程配置调优**: 根据问题规模调整最优的work-group配置

#### 14.1.2 第二阶段：深度优化（提交2）
- **向量化计算**: 利用GPU SIMD能力，实现向量化操作
- **循环展开**: 减少循环开销，提高计算密度
- **数据预取**: 实现异步数据预取，隐藏访存延迟

#### 14.1.3 第三阶段：极致优化（提交3）
- **算法创新**: 在约束条件下实现创新的计算策略
- **细节调优**: 针对特定硬件特性进行微调
- **性能验证**: 确保在各种测试用例下的稳定性能

### 14.2 技术发展方向
#### 14.2.1 短期目标
- **性能提升**: 在比赛约束下实现3-5倍性能提升
- **稳定性保证**: 确保在各种参数配置下的正确性
- **代码优化**: 提高代码可读性和可维护性

#### 14.2.2 长期愿景
- **技术积累**: 为后续GPU优化项目积累经验
- **算法研究**: 深入研究卷积优化的前沿算法
- **生态贡献**: 为开源GPU计算生态做出贡献

## 15. 项目总结与技术价值

### 15.1 比赛成果总结
本项目在卷积算子优化竞赛的框架下，成功实现了基于HIP的高性能2D卷积优化。项目严格遵循比赛规则，在接口约束、存储格式限制、第三方库禁用等多重约束下，通过深入的算法分析和系统优化，实现了显著的性能提升。

### 15.2 技术创新点
- **约束优化**: 在严格的比赛规则约束下实现了创新的优化策略
- **接口设计**: 巧妙利用预留字段扩展优化参数，保持接口兼容性
- **内存优化**: 在NCHW格式约束下实现了高效的内存访问模式
- **性能验证**: 建立了完整的性能测试和正确性验证体系

### 15.3 学习价值
- **GPU编程**: 深入理解AMD GPU架构和HIP编程模型
- **并行优化**: 掌握GPU并行计算的核心优化技术
- **约束求解**: 学会在多重约束条件下寻找最优解决方案
- **工程实践**: 体验从算法设计到性能调优的完整工程流程

### 15.4 未来展望
通过本次比赛项目的实践，不仅提升了GPU并行计算的技术能力，更重要的是培养了在约束条件下进行创新优化的工程思维。这些经验和技术积累将为未来在高性能计算、深度学习加速等领域的发展奠定坚实基础。

随着异构计算和AI加速需求的不断增长，卷积优化技术将在更广泛的应用场景中发挥重要作用，本项目的技术成果和优化思路具有重要的参考价值和推广意义。
