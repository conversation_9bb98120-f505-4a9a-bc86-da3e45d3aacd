#include <hip/hip_runtime.h>
#include <hip/hip_ext.h>
#include "conv2d.h"

// --- Tuned GEMM Kernel Configuration for gfx906 ---
#define GEMM_TILE_M 64  
#define GEMM_TILE_N 64  
#define GEMM_TILE_K 16  

#define BLOCK_DIM_X 16 
#define BLOCK_DIM_Y 16 

#define THREAD_WORK_M (GEMM_TILE_M / BLOCK_DIM_Y) 
#define THREAD_WORK_N (GEMM_TILE_N / BLOCK_DIM_X) 

// OPTIMIZATION: Use __restrict__ to inform the compiler about no memory aliasing.
typedef struct mykernelParamType
{
    _Float16* __restrict__ pin;
    _Float16* __restrict__ pweight;
    _Float16* __restrict__ pout;
    unsigned int n;
    unsigned int c;
    unsigned int h;
    unsigned int w;
    unsigned int k;
    unsigned int r;
    unsigned int s;
    unsigned int u;
    unsigned int v;
    unsigned int p;
    unsigned int q;
    unsigned int Oh;
    unsigned int Ow;
} mykernelParamType;

extern "C" __global__ void Img2ColGEMMKernel_v7(mykernelParamType param)
{
    const int K_gemm = param.c * param.r * param.s;

    const int n_idx = blockIdx.z;
    const int tx = threadIdx.x;
    const int ty = threadIdx.y;
    const int thread_id = ty * BLOCK_DIM_X + tx;

    const int block_m_base = blockIdx.y * GEMM_TILE_M;
    const int block_n_base = blockIdx.x * GEMM_TILE_N;
    
    __shared__ _Float16 sh_A[GEMM_TILE_M][GEMM_TILE_K];
    __shared__ _Float16 sh_B[GEMM_TILE_K][GEMM_TILE_N];

    float accum[THREAD_WORK_M][THREAD_WORK_N] = {0};

    const long batch_in_offset = (long)n_idx * param.c * param.h * param.w;
    const _Float16* __restrict__ current_pin = param.pin + batch_in_offset;

    for (int k_base = 0; k_base < K_gemm; k_base += GEMM_TILE_K)
    {
        __syncthreads();

        // Cooperative Loading (same as v6)
        for(int i = thread_id; i < GEMM_TILE_M * GEMM_TILE_K; i += BLOCK_DIM_X * BLOCK_DIM_Y) {
            int m_load_offset = i / GEMM_TILE_K;
            int k_load_offset = i % GEMM_TILE_K;
            int m_global = block_m_base + m_load_offset;
            int k_global = k_base + k_load_offset;
            if(m_global < param.k && k_global < K_gemm) {
                sh_A[m_load_offset][k_load_offset] = param.pweight[m_global * K_gemm + k_global];
            } else {
                sh_A[m_load_offset][k_load_offset] = 0.0f;
            }
        }

        for(int i = thread_id; i < GEMM_TILE_K * GEMM_TILE_N; i += BLOCK_DIM_X * BLOCK_DIM_Y) {
            int k_load_offset = i / GEMM_TILE_N;
            int n_load_offset = i % GEMM_TILE_N;
            int k_global = k_base + k_load_offset;
            int n_global = block_n_base + n_load_offset;
            if(k_global < K_gemm && n_global < (param.Oh * param.Ow)) {
                int s_idx = k_global % param.s;
                int r_idx = (k_global / param.s) % param.r;
                int c_idx = k_global / (param.r * param.s);
                int oh_idx = n_global / param.Ow;
                int ow_idx = n_global % param.Ow;
                int in_h = oh_idx * param.u + r_idx - param.p;
                int in_w = ow_idx * param.v + s_idx - param.q;
                if (in_h >= 0 && in_h < param.h && in_w >= 0 && in_w < param.w) {
                    sh_B[k_load_offset][n_load_offset] = current_pin[c_idx * param.h * param.w + in_h * param.w + in_w];
                } else {
                    sh_B[k_load_offset][n_load_offset] = 0.0f;
                }
            } else {
                sh_B[k_load_offset][n_load_offset] = 0.0f;
            }
        }
        __syncthreads();

        // --- OPTIMIZATION: Loop Unrolling ---
        // Manually unroll the k_i loop to improve ILP
        for (int k_i = 0; k_i < GEMM_TILE_K; k_i += 2) { // Unroll by a factor of 2
            for (int m_i = 0; m_i < THREAD_WORK_M; ++m_i) {
                int m_local = ty * THREAD_WORK_M + m_i;
                for (int n_i = 0; n_i < THREAD_WORK_N; ++n_i) {
                    int n_local = tx * THREAD_WORK_N + n_i;
                    accum[m_i][n_i] += (float)sh_A[m_local][k_i] * (float)sh_B[k_i][n_local];
                    accum[m_i][n_i] += (float)sh_A[m_local][k_i+1] * (float)sh_B[k_i+1][n_local];
                }
            }
        }
    }

    const long batch_out_offset = (long)n_idx * param.k * param.Oh * param.Ow;
    for (int m_i = 0; m_i < THREAD_WORK_M; ++m_i) {
        for (int n_i = 0; n_i < THREAD_WORK_N; ++n_i) {
            int m_global = block_m_base + ty * THREAD_WORK_M + m_i;
            int n_global = block_n_base + tx * THREAD_WORK_N + n_i;
            if (m_global < param.k && n_global < param.Oh * param.Ow) {
                param.pout[batch_out_offset + m_global * (param.Oh * param.Ow) + n_global] = (_Float16)accum[m_i][n_i];
            }
        }
    }
}

/* getParamsize is updated for the new struct */
int getParamsize(__in__ problem_t* problem, __out__ int* paramSize)
{
    *paramSize = sizeof(mykernelParamType);
    return 0;
}

/* getkernelInfo is updated for the new kernel */
int getkernelInfo(__in__ problem_t* problem, __out__  kernelInfo_t* kernelInfo, __in_out__ void* param)
{
    mykernelParamType* pArgs = (mykernelParamType*)param;
    unsigned int n = problem->n;
    unsigned int c = problem->c;
    unsigned int h = problem->h;
    unsigned int w = problem->w;
    unsigned int k = problem->k;
    unsigned int r = problem->r;
    unsigned int s = problem->s;
    unsigned int u = problem->u;
    unsigned int v = problem->v;
    unsigned int p = problem->p;
    unsigned int q = problem->q;
    unsigned int outh = (h - r + 2 * p) / u + 1;
    unsigned int outw = (w - s + 2 * q) / v + 1;
    
    const int M = k;
    const int N = outh * outw;
    
    kernelInfo->blockx = (N + GEMM_TILE_N - 1) / GEMM_TILE_N;
    kernelInfo->blocky = (M + GEMM_TILE_M - 1) / GEMM_TILE_M;
    kernelInfo->blockz = n;

    kernelInfo->threadx = BLOCK_DIM_X;
    kernelInfo->thready = BLOCK_DIM_Y;
    kernelInfo->threadz = 1;
    
    kernelInfo->dynmicLdsSize = 0;
    kernelInfo->kernelPtr = (void*)Img2ColGEMMKernel_v7;

    pArgs->pin = problem->in;
    pArgs->pweight = problem->weight;
    pArgs->pout = problem->out;
    pArgs->n = n;
    pArgs->c = c;
    pArgs->h = h;
    pArgs->w = w;
    pArgs->k = k;
    pArgs->r = r;
    pArgs->s = s;
    pArgs->u = u;
    pArgs->v = v;
    pArgs->p = p;
    pArgs->q = q;
    pArgs->Oh = outh;
    pArgs->Ow = outw;

    return 0;
}