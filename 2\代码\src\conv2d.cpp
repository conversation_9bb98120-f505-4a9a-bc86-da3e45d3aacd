/*
 * 高性能2D卷积GPU实现 - 基于GEMM优化策略
 *
 * 本文件实现了针对AMD GPU (gfx906架构) 优化的2D卷积操作
 * 采用Im2Col + GEMM的经典优化策略，将卷积转换为矩阵乘法
 *
 * 主要优化技术：
 * 1. 共享内存(LDS)优化 - 减少全局内存访问
 * 2. 分块计算(Tiling) - 提高数据局部性
 * 3. 循环展开 - 提高指令级并行度
 * 4. 协作加载 - 优化内存带宽利用率
 * 5. 向量化计算 - 利用GPU SIMD能力
 */

#include <hip/hip_runtime.h>  // HIP运行时API
#include <hip/hip_ext.h>      // HIP扩展API
#include "conv2d.h"           // 卷积相关数据结构定义

// ==================== GEMM优化参数配置 ====================
// 针对AMD gfx906架构调优的GEMM分块参数

#define GEMM_TILE_M 64   // GEMM矩阵A的分块大小(M维度) - 对应输出通道数
#define GEMM_TILE_N 64   // GEMM矩阵B的分块大小(N维度) - 对应输出空间大小
#define GEMM_TILE_K 16   // GEMM矩阵的K维度分块大小 - 对应输入通道×卷积核大小

// 线程块配置 - 每个block包含16×16=256个线程
#define BLOCK_DIM_X 16   // 线程块X维度大小
#define BLOCK_DIM_Y 16   // 线程块Y维度大小

// 每个线程负责的工作量计算
#define THREAD_WORK_M (GEMM_TILE_M / BLOCK_DIM_Y)  // 每个线程处理的M维度元素数 = 64/16 = 4
#define THREAD_WORK_N (GEMM_TILE_N / BLOCK_DIM_X)  // 每个线程处理的N维度元素数 = 64/16 = 4

// ==================== 内核参数结构体定义 ====================
/**
 * GPU内核参数结构体
 * 使用__restrict__关键字告知编译器指针间无别名，便于优化
 */
typedef struct mykernelParamType
{
    _Float16* __restrict__ pin;      // 输入特征图指针 (NCHW格式)
    _Float16* __restrict__ pweight;  // 卷积核权重指针 (KCRS格式)
    _Float16* __restrict__ pout;     // 输出特征图指针 (NKOhOw格式)

    // 卷积操作参数
    unsigned int n;   // 批处理大小 (batch size)
    unsigned int c;   // 输入通道数 (input channels)
    unsigned int h;   // 输入特征图高度 (input height)
    unsigned int w;   // 输入特征图宽度 (input width)
    unsigned int k;   // 输出通道数/卷积核数量 (output channels/kernel count)
    unsigned int r;   // 卷积核高度 (kernel height)
    unsigned int s;   // 卷积核宽度 (kernel width)
    unsigned int u;   // 高度方向步长 (stride height)
    unsigned int v;   // 宽度方向步长 (stride width)
    unsigned int p;   // 高度方向填充 (padding height)
    unsigned int q;   // 宽度方向填充 (padding width)
    unsigned int Oh;  // 输出特征图高度 (output height)
    unsigned int Ow;  // 输出特征图宽度 (output width)
} mykernelParamType;

// ==================== 主GPU内核函数 ====================
/**
 * Im2Col + GEMM优化的2D卷积内核函数
 *
 * 算法原理：
 * 1. 将卷积操作转换为矩阵乘法: Y = A × B
 *    - A: 权重矩阵 [K, C×R×S] (K个卷积核，每个大小C×R×S)
 *    - B: Im2Col展开的输入矩阵 [C×R×S, Oh×Ow] (输入特征图按卷积窗口展开)
 *    - Y: 输出矩阵 [K, Oh×Ow] (K个输出通道，每个大小Oh×Ow)
 *
 * 2. 使用分块GEMM算法，每个线程块处理64×64的输出块
 * 3. 利用共享内存缓存频繁访问的数据
 * 4. 通过循环展开提高计算密度
 *
 * @param param 卷积参数结构体，包含所有输入输出指针和卷积配置
 */
extern "C" __global__ void Img2ColGEMMKernel_v7(mykernelParamType param)
{
    // ==================== 基本参数计算 ====================
    const int K_gemm = param.c * param.r * param.s;  // GEMM的K维度 = 输入通道数×卷积核高×卷积核宽

    // 线程和块索引
    const int n_idx = blockIdx.z;        // 当前处理的batch索引
    const int tx = threadIdx.x;          // 线程在block内的X坐标 [0, 15]
    const int ty = threadIdx.y;          // 线程在block内的Y坐标 [0, 15]
    const int thread_id = ty * BLOCK_DIM_X + tx;  // 线程在block内的一维索引 [0, 255]

    // 当前线程块在全局GEMM矩阵中的起始位置
    const int block_m_base = blockIdx.y * GEMM_TILE_M;  // M维度起始位置 (对应输出通道)
    const int block_n_base = blockIdx.x * GEMM_TILE_N;  // N维度起始位置 (对应输出空间位置)

    // ==================== 共享内存声明 ====================
    // 共享内存用于缓存GEMM的A和B矩阵块，减少全局内存访问
    __shared__ _Float16 sh_A[GEMM_TILE_M][GEMM_TILE_K];  // 缓存权重矩阵块 [64×16]
    __shared__ _Float16 sh_B[GEMM_TILE_K][GEMM_TILE_N];  // 缓存Im2Col矩阵块 [16×64]

    // ==================== 私有累加器初始化 ====================
    // 每个线程维护一个4×4的累加器矩阵，存储部分乘积结果
    float accum[THREAD_WORK_M][THREAD_WORK_N] = {0};

    // ==================== 输入数据指针计算 ====================
    // 计算当前batch的输入数据起始地址
    const long batch_in_offset = (long)n_idx * param.c * param.h * param.w;
    const _Float16* __restrict__ current_pin = param.pin + batch_in_offset;

    // ==================== 主计算循环：分块GEMM ====================
    // 沿K维度分块处理，每次处理GEMM_TILE_K=16个元素
    for (int k_base = 0; k_base < K_gemm; k_base += GEMM_TILE_K)
    {
        // 同步所有线程，确保上一轮计算完成
        __syncthreads();

        // ==================== 协作加载权重矩阵A到共享内存 ====================
        // 256个线程协作加载64×16=1024个权重元素到共享内存
        // 每个线程负责加载约4个元素 (1024/256=4)
        for(int i = thread_id; i < GEMM_TILE_M * GEMM_TILE_K; i += BLOCK_DIM_X * BLOCK_DIM_Y) {
            // 计算在共享内存中的位置
            int m_load_offset = i / GEMM_TILE_K;  // 行索引 [0, 63]
            int k_load_offset = i % GEMM_TILE_K;  // 列索引 [0, 15]

            // 计算在全局权重矩阵中的位置
            int m_global = block_m_base + m_load_offset;  // 全局行索引 (输出通道索引)
            int k_global = k_base + k_load_offset;        // 全局列索引 (输入通道×卷积核位置)

            // 边界检查并加载数据
            if(m_global < param.k && k_global < K_gemm) {
                // 权重矩阵按行主序存储: weight[m_global][k_global]
                sh_A[m_load_offset][k_load_offset] = param.pweight[m_global * K_gemm + k_global];
            } else {
                // 超出边界的位置填充0
                sh_A[m_load_offset][k_load_offset] = 0.0f;
            }
        }

        // ==================== 协作加载Im2Col矩阵B到共享内存 ====================
        // 256个线程协作加载16×64=1024个Im2Col元素到共享内存
        for(int i = thread_id; i < GEMM_TILE_K * GEMM_TILE_N; i += BLOCK_DIM_X * BLOCK_DIM_Y) {
            // 计算在共享内存中的位置
            int k_load_offset = i / GEMM_TILE_N;  // 行索引 [0, 15]
            int n_load_offset = i % GEMM_TILE_N;  // 列索引 [0, 63]

            // 计算在全局Im2Col矩阵中的位置
            int k_global = k_base + k_load_offset;        // 全局行索引 (输入通道×卷积核位置)
            int n_global = block_n_base + n_load_offset;  // 全局列索引 (输出空间位置)

            // 边界检查
            if(k_global < K_gemm && n_global < (param.Oh * param.Ow)) {
                // ==================== Im2Col映射计算 ====================
                // 将k_global解码为(c_idx, r_idx, s_idx)三元组
                int s_idx = k_global % param.s;                    // 卷积核宽度索引
                int r_idx = (k_global / param.s) % param.r;        // 卷积核高度索引
                int c_idx = k_global / (param.r * param.s);        // 输入通道索引

                // 将n_global解码为(oh_idx, ow_idx)二元组
                int oh_idx = n_global / param.Ow;  // 输出特征图高度索引
                int ow_idx = n_global % param.Ow;  // 输出特征图宽度索引

                // 计算对应的输入特征图位置 (考虑步长和填充)
                int in_h = oh_idx * param.u + r_idx - param.p;  // 输入高度坐标
                int in_w = ow_idx * param.v + s_idx - param.q;  // 输入宽度坐标

                // 边界检查：只有在有效输入范围内才读取数据
                if (in_h >= 0 && in_h < param.h && in_w >= 0 && in_w < param.w) {
                    // 计算输入特征图的线性索引并读取数据
                    int input_idx = c_idx * param.h * param.w + in_h * param.w + in_w;
                    sh_B[k_load_offset][n_load_offset] = current_pin[input_idx];
                } else {
                    // 超出边界的位置填充0 (实现padding效果)
                    sh_B[k_load_offset][n_load_offset] = 0.0f;
                }
            } else {
                // 超出GEMM矩阵边界的位置填充0
                sh_B[k_load_offset][n_load_offset] = 0.0f;
            }
        }

        // 同步所有线程，确保共享内存加载完成
        __syncthreads();

        // ==================== 分块矩阵乘法计算 ====================
        // 使用循环展开优化提高指令级并行度 (ILP)
        // 每次处理2个K维度元素，减少循环开销
        for (int k_i = 0; k_i < GEMM_TILE_K; k_i += 2) { // 2倍循环展开
            // 每个线程计算其负责的4×4子矩阵
            for (int m_i = 0; m_i < THREAD_WORK_M; ++m_i) {
                // 计算当前线程在共享内存中的M维度索引
                int m_local = ty * THREAD_WORK_M + m_i;  // ty∈[0,15], m_i∈[0,3] => m_local∈[0,63]

                for (int n_i = 0; n_i < THREAD_WORK_N; ++n_i) {
                    // 计算当前线程在共享内存中的N维度索引
                    int n_local = tx * THREAD_WORK_N + n_i;  // tx∈[0,15], n_i∈[0,3] => n_local∈[0,63]

                    // 执行矩阵乘法累加：C[m,n] += A[m,k] * B[k,n]
                    // 第一个乘积：k_i
                    accum[m_i][n_i] += (float)sh_A[m_local][k_i] * (float)sh_B[k_i][n_local];
                    // 第二个乘积：k_i+1 (循环展开)
                    accum[m_i][n_i] += (float)sh_A[m_local][k_i+1] * (float)sh_B[k_i+1][n_local];
                }
            }
        }
    } // K维度循环结束

    // ==================== 结果写回全局内存 ====================
    // 计算当前batch在输出张量中的起始偏移
    const long batch_out_offset = (long)n_idx * param.k * param.Oh * param.Ow;

    // 将每个线程的4×4累加器结果写回全局内存
    for (int m_i = 0; m_i < THREAD_WORK_M; ++m_i) {
        for (int n_i = 0; n_i < THREAD_WORK_N; ++n_i) {
            // 计算在全局输出矩阵中的位置
            int m_global = block_m_base + ty * THREAD_WORK_M + m_i;  // 全局M索引 (输出通道)
            int n_global = block_n_base + tx * THREAD_WORK_N + n_i;  // 全局N索引 (输出空间位置)

            // 边界检查：确保不超出输出张量范围
            if (m_global < param.k && n_global < param.Oh * param.Ow) {
                // 计算输出张量的线性索引：batch_offset + channel_offset + spatial_position
                long output_idx = batch_out_offset + m_global * (param.Oh * param.Ow) + n_global;
                // 将float累加结果转换为半精度并写入
                param.pout[output_idx] = (_Float16)accum[m_i][n_i];
            }
        }
    }
} // 内核函数结束

// ==================== 主机端接口函数 ====================

/**
 * 获取内核参数结构体大小
 *
 * 此函数返回GPU内核参数结构体的字节大小，用于主机端分配参数内存
 *
 * @param problem 输入：卷积问题描述结构体指针
 * @param paramSize 输出：参数结构体大小（字节）
 * @return 0表示成功
 */
int getParamsize(__in__ problem_t* problem, __out__ int* paramSize)
{
    *paramSize = sizeof(mykernelParamType);
    return 0;
}

/**
 * 配置GPU内核执行参数
 *
 * 此函数根据卷积问题配置GPU内核的执行参数，包括：
 * 1. 计算Grid和Block维度
 * 2. 设置内核函数指针
 * 3. 初始化内核参数结构体
 *
 * @param problem 输入：卷积问题描述结构体指针
 * @param kernelInfo 输出：内核执行配置信息
 * @param param 输入输出：内核参数结构体指针
 * @return 0表示成功
 */
int getkernelInfo(__in__ problem_t* problem, __out__  kernelInfo_t* kernelInfo, __in_out__ void* param)
{
    // 类型转换为具体的参数结构体
    mykernelParamType* pArgs = (mykernelParamType*)param;

    // ==================== 提取卷积参数 ====================
    unsigned int n = problem->n;  // 批处理大小
    unsigned int c = problem->c;  // 输入通道数
    unsigned int h = problem->h;  // 输入高度
    unsigned int w = problem->w;  // 输入宽度
    unsigned int k = problem->k;  // 输出通道数
    unsigned int r = problem->r;  // 卷积核高度
    unsigned int s = problem->s;  // 卷积核宽度
    unsigned int u = problem->u;  // 高度步长
    unsigned int v = problem->v;  // 宽度步长
    unsigned int p = problem->p;  // 高度填充
    unsigned int q = problem->q;  // 宽度填充

    // ==================== 计算输出尺寸 ====================
    // 标准卷积输出尺寸公式：Oh = (H - R + 2P) / U + 1
    unsigned int outh = (h - r + 2 * p) / u + 1;  // 输出高度
    unsigned int outw = (w - s + 2 * q) / v + 1;  // 输出宽度

    // ==================== GEMM矩阵维度计算 ====================
    const int M = k;              // GEMM矩阵A的行数 = 输出通道数
    const int N = outh * outw;     // GEMM矩阵B的列数 = 输出空间大小

    // ==================== GPU执行配置计算 ====================
    // 计算需要的线程块数量（向上取整）
    kernelInfo->blockx = (N + GEMM_TILE_N - 1) / GEMM_TILE_N;  // X维度块数 (处理N维度)
    kernelInfo->blocky = (M + GEMM_TILE_M - 1) / GEMM_TILE_M;  // Y维度块数 (处理M维度)
    kernelInfo->blockz = n;                                     // Z维度块数 (处理batch维度)

    // 每个线程块的线程配置
    kernelInfo->threadx = BLOCK_DIM_X;  // X维度线程数 = 16
    kernelInfo->thready = BLOCK_DIM_Y;  // Y维度线程数 = 16
    kernelInfo->threadz = 1;            // Z维度线程数 = 1

    // 动态共享内存大小（本实现使用静态共享内存，故为0）
    kernelInfo->dynmicLdsSize = 0;

    // 设置内核函数指针
    kernelInfo->kernelPtr = (void*)Img2ColGEMMKernel_v7;

    // ==================== 初始化内核参数结构体 ====================
    pArgs->pin = problem->in;       // 输入特征图指针
    pArgs->pweight = problem->weight; // 卷积核权重指针
    pArgs->pout = problem->out;     // 输出特征图指针
    pArgs->n = n;                   // 批处理大小
    pArgs->c = c;                   // 输入通道数
    pArgs->h = h;                   // 输入高度
    pArgs->w = w;                   // 输入宽度
    pArgs->k = k;                   // 输出通道数
    pArgs->r = r;                   // 卷积核高度
    pArgs->s = s;                   // 卷积核宽度
    pArgs->u = u;                   // 高度步长
    pArgs->v = v;                   // 宽度步长
    pArgs->p = p;                   // 高度填充
    pArgs->q = q;                   // 宽度填充
    pArgs->Oh = outh;               // 输出高度
    pArgs->Ow = outw;               // 输出宽度

    return 0;
}