/*
 * CPU参考实现验证头文件
 *
 * 提供2D卷积的CPU参考实现，用于验证GPU计算结果的正确性
 * 采用标准的直接卷积算法，保证计算精度和正确性
 */

#ifndef __VERFIY_HEADER__
#define __VERFIY_HEADER__

/**
 * CPU版本2D卷积参考实现
 *
 * 使用标准的直接卷积算法，通过多重嵌套循环实现卷积操作
 * 支持任意的步长(stride)和填充(padding)配置
 *
 * 算法流程：
 * 1. 遍历每个batch和输出通道
 * 2. 遍历输出特征图的每个位置
 * 3. 对每个输出位置，计算对应的卷积窗口内所有元素的加权和
 * 4. 处理边界填充，超出输入范围的位置视为0
 *
 * @param pin    输入特征图指针 [N×C×H×W]
 * @param pwei   卷积核权重指针 [K×C×R×S]
 * @param pout   输出特征图指针 [N×K×Oh×Ow]
 * @param n      批处理大小
 * @param c      输入通道数
 * @param h      输入特征图高度
 * @param w      输入特征图宽度
 * @param k      输出通道数/卷积核数量
 * @param r      卷积核高度
 * @param s      卷积核宽度
 * @param u      高度方向步长
 * @param v      宽度方向步长
 * @param p      高度方向填充
 * @param q      宽度方向填充
 */
void conv2dcpu(_Float16* pin, _Float16* pwei, _Float16* pout,
               int n, int c, int h, int w, int k, int r, int s,
               int u, int v, int p, int q)
{
    // ==================== 计算输出尺寸 ====================
    // 注意：这里的公式与GPU版本略有不同（数学上等价）
    // CPU版本：(h + 2*p - r)/u + 1
    // GPU版本：(h - r + 2*p)/u + 1
    int oh = (h + 2*p - r)/u + 1;  // 输出特征图高度
    int ow = (w + 2*q - s)/v + 1;  // 输出特征图宽度

    // ==================== 六重嵌套循环实现卷积 ====================
    #pragma unroll  // 编译器优化提示：展开循环

    // 遍历每个batch
    for(int nNum = 0; nNum < n; nNum++)
    {
        // 遍历每个输出通道
        for(int kNum = 0; kNum < k; kNum++)
        {
            // 遍历输出特征图的每个位置
            for(int i = 0; i < oh; i++)        // 输出高度维度
            {
                for(int j = 0; j < ow; j++)    // 输出宽度维度
                {
                    double sum = 0.0;  // 使用double精度进行累加，提高数值精度

                    // 计算当前输出位置对应的输入起始坐标（考虑步长和填充）
                    int posh = i*u - p;  // 输入高度起始位置
                    int posw = j*v - q;  // 输入宽度起始位置

                    // 遍历卷积核窗口
                    for(int cNum = 0; cNum < c; cNum++)        // 输入通道维度
                    {
                        for(int khNum = 0; khNum < r; khNum++) // 卷积核高度维度
                        {
                            for(int kwNum = 0; kwNum < s; kwNum++) // 卷积核宽度维度
                            {
                                // 计算当前卷积核位置对应的输入坐标
                                int posh_ori = posh + khNum;  // 实际输入高度坐标
                                int posw_ori = posw + kwNum;  // 实际输入宽度坐标

                                // 边界检查：只处理有效的输入范围
                                if(posw_ori >= 0 && posh_ori >= 0 && posw_ori < w && posh_ori < h)
                                {
                                    // 计算输入和权重的线性索引
                                    int input_idx = nNum*c*h*w + cNum*(w*h) + posh_ori*w + posw_ori;
                                    int weight_idx = kNum*r*s*c + cNum*r*s + khNum*s + kwNum;

                                    // 执行卷积乘积累加
                                    sum += (double)(pin[input_idx] * pwei[weight_idx]);
                                }
                                // 超出边界的位置自动视为0（实现padding效果）
                            }
                        }
                    }

                    // 将累加结果写入输出特征图
                    int output_idx = nNum*k*oh*ow + kNum*oh*ow + i*ow + j;
                    pout[output_idx] = (_Float16)sum;  // 转换为半精度
                }
            }
        }
    }
}

#endif // __VERFIY_HEADER__