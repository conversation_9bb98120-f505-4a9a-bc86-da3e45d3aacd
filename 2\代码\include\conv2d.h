/*
 * 2D卷积GPU优化头文件
 *
 * 定义了卷积操作相关的数据结构和接口函数
 * 支持半精度浮点数(_Float16)的2D卷积计算
 *
 * 数据布局约定：
 * - 输入特征图：NCHW格式 (Batch, Channel, Height, Width)
 * - 卷积核权重：KCRS格式 (Kernel_count, Channel, Kernel_height, Kernel_width)
 * - 输出特征图：NKOhOw格式 (Batch, Kernel_count, Output_height, Output_width)
 */

#ifndef __CONV2D_FP16_FWD_HEADER__
#define __CONV2D_FP16_FWD_HEADER__

// ==================== 参数方向标识宏定义 ====================
#define __in__      // 输入参数标识
#define __out__     // 输出参数标识
#define __in_out__  // 输入输出参数标识

// ==================== 卷积问题描述结构体 ====================
/**
 * 卷积问题描述结构体
 * 包含卷积操作的所有输入输出数据指针和配置参数
 */
typedef struct
{
    // 数据指针（GPU设备端地址）
    _Float16*   in;      // 输入特征图数据地址 [N×C×H×W]
    _Float16*   weight;  // 卷积核权重数据地址 [K×C×R×S]
    _Float16*   out;     // 输出特征图数据地址 [N×K×Oh×Ow]

    // 卷积操作参数
    unsigned int n;      // 批处理大小 (batch size)              默认值: 1
    unsigned int c;      // 输入通道数 (input channel number)    默认值: 32
    unsigned int h;      // 输入特征图高度 (input height)        默认值: 32
    unsigned int w;      // 输入特征图宽度 (input width)         默认值: 32
    unsigned int k;      // 输出通道数/卷积核数量 (kernel count) 默认值: 32
    unsigned int r;      // 卷积核高度 (kernel height)           默认值: 1
    unsigned int s;      // 卷积核宽度 (kernel width)            默认值: 1
    unsigned int u;      // 高度方向步长 (stride height)         默认值: 1
    unsigned int v;      // 宽度方向步长 (stride width)          默认值: 1
    unsigned int p;      // 高度方向填充 (padding height)        默认值: 0
    unsigned int q;      // 宽度方向填充 (padding width)         默认值: 0
} problem_t;

// ==================== GPU内核执行配置结构体 ====================
/**
 * GPU内核执行配置信息结构体
 * 包含GPU内核启动所需的Grid/Block配置和内核函数指针
 */
typedef struct
{
    // GPU执行网格配置
    unsigned int blockx;         // X维度的线程块数量
    unsigned int blocky;         // Y维度的线程块数量
    unsigned int blockz;         // Z维度的线程块数量

    // 线程块内线程配置
    unsigned int threadx;        // 每个线程块X维度的线程数
    unsigned int thready;        // 每个线程块Y维度的线程数
    unsigned int threadz;        // 每个线程块Z维度的线程数

    // 共享内存配置
    unsigned int dynmicLdsSize;  // 动态分配的LDS(共享内存)大小，不使用时为0

    // 内核函数指针
    void* kernelPtr;             // GPU内核函数指针
} kernelInfo_t;

// ==================== 接口函数声明 ====================

/**
 * 获取内核参数结构体大小
 * @param problem 输入：卷积问题描述
 * @param paramSize 输出：参数结构体字节大小
 * @return 0表示成功
 */
int getParamsize(__in__ problem_t* problem, __out__ int* paramSize);

/**
 * 配置GPU内核执行参数
 * @param problem 输入：卷积问题描述
 * @param kernelInfo 输出：内核执行配置信息
 * @param param 输入输出：内核参数结构体
 * @return 0表示成功
 */
int getkernelInfo(__in__ problem_t* problem, __out__ kernelInfo_t* kernelInfo, __in_out__ void* param);

#endif // __CONV2D_FP16_FWD_HEADER__