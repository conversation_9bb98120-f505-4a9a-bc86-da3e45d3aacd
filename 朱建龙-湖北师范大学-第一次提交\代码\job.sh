#!/bin/bash
#SBATCH -J test                         # 作业名
#SBATCH -p kshdnormal                 # 队列名  使用whichpartition 查看
#SBATCH -N 1                            # 节点数量
#SBATCH --ntasks-per-node=1             # 每节点运行进程数
#SBATCH -c 8                            # 每个进程所用cpu核数
#SBATCH --gres=dcu:1                    # 每个节点申请的dcu数量
#SBATCH -o %j.out                       # 作业标准输出
#SBATCH -e %j.out                       # 作业错误输出，这里两种输出放在了一个文件中显示
module list

module load anaconda3/5.2.0
module load compiler/devtoolset/7.3.1
module load mpi/hpcx/gcc-7.3.1
module load compiler/dtk/25.04
module list

cd /public/home/<USER>/1/ # 根据实际情况修改
make clean
make

./conv2dfp16demo  128 3   225 225 32  3   3   2   2   0   0
./conv2dfp16demo  49  128 35  35  384 3   3   2   2   0   0
./conv2dfp16demo  16  128 105 105 256 3   3   2   2   0   0
./conv2dfp16demo  128 3   230 230 64  7   7   2   2   0   0
./conv2dfp16demo  2   3   838 1350    64  7   7   2   2   0   0
./conv2dfp16demo  256 256 28  28  256 2   2   2   2   0   0