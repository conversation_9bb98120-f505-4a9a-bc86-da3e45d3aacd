#
# 2D卷积GPU优化项目构建配置文件
#
# 本Makefile用于编译基于HIP的2D卷积GPU优化实现
# 目标平台：AMD GPU gfx906架构
# 编程模型：HIP (Heterogeneous-compute Interface for Portability)
#

# ==================== 编译器配置 ====================
# 使用HIP编译器，支持GPU代码编译
CC=$(HIP_PATH)/bin/hipcc

# ==================== 编译选项配置 ====================
# -DHIP_ROCM: 启用ROCm平台支持
# -DNDEBUG: 禁用调试断言，优化性能
# -DUSE_DEFAULT_STDLIB: 使用默认标准库
# --offload-arch=gfx906: 指定目标GPU架构为gfx906
# -g: 生成调试信息
CXXFLAGS += -DHIP_ROCM -DNDEBUG -DUSE_DEFAULT_STDLIB --offload-arch=gfx906 -g

# ==================== 头文件路径配置 ====================
# 包含HIP运行时头文件和项目本地头文件
INCLUDES += -I$(HIP_PATH)/include -I./include

# ==================== 链接选项配置 ====================
# 当前项目无需额外链接库
LDFLAGS =

# ==================== 源文件和目标文件配置 ====================
# 自动获取src目录下的所有cpp文件
CUR_SOURCE=${wildcard ./src/*.cpp}

# 将cpp文件名转换为对应的目标文件名(.o)
CUR_OBJS=${patsubst %.cpp, %.o, $(CUR_SOURCE)}

# ==================== 可执行文件名配置 ====================
EXECUTABLE=conv2dfp16demo

# ==================== 构建规则 ====================

# 默认目标：构建可执行文件
all:$(EXECUTABLE)

# 链接规则：将所有目标文件链接成可执行文件
$(EXECUTABLE): $(CUR_OBJS)
	$(CC) $(CUR_OBJS) $(LDFLAGS) -o $(EXECUTABLE)

# 编译规则：将cpp源文件编译成目标文件
# -c: 只编译不链接
# -w: 禁用警告信息
%.o:%.cpp
	$(CC) -c -w $< $(CXXFLAGS) $(INCLUDES) -o $@

# ==================== 清理规则 ====================
# 删除生成的可执行文件和目标文件
clean:
	rm -f $(EXECUTABLE)
	rm -f ./src/*.o
